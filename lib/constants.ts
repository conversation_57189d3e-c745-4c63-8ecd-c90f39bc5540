// ======================== config keys ========================
// Analytics
export const MIXPANEL_TOKEN = "b3b523ed773c1ee5e3a44ed7f0f84a2c";
export const CLARITY_TOKEN = "srxy8kse83";
export const GTM_TOKEN = "NKGP52HQ"; // Google Tag Manager

// ======================== utils keys ========================
export const WEBDOMAIN = "fastlipsync.com";
export const WEBHOST = process.env.NODE_ENV === "production" ? `https://${WEBDOMAIN}` : "http://localhost:3000";
export const WEBNAME = "FastLipsync";
export const EMAIL_CONTACT = `hello@${WEBDOMAIN}`;
export const CALLBACK_URL_FAL = process.env.NODE_ENV === "production" ? `${WEBHOST}/api/webhook/fal` : "https://dev-next.xav.im/api/webhook/fal";

// Auth
export const ROUTE_PATH_SIGN_IN = "/";
export const ROUTE_PATH_SIGN_IN_AFTER = "/";

// OSS
export const CLOUDFLARE_R2_BUCKET_NAME = "fastlipsync";
export const OSS_URL_HOST = `https://static.${WEBDOMAIN}/`;

// Cookie
export const COOKIE_BROWSER_USER_ID = "unia"; // 登录后把userId存入cookie，用于多次登录创建新账户，如果多次创建新账号则没有免费额度

// Duration
export const DURATION_1_HOUR = 60 * 60;
export const DURATION_2_HOUR = 2 * DURATION_1_HOUR;
export const DURATION_1_DAY = 24 * DURATION_1_HOUR;
export const DURATION_1_WEEK = 7 * DURATION_1_DAY;
export const DURATION_1_MONTH = 30 * DURATION_1_DAY;
export const DURATION_6_MONTH = 180 * DURATION_1_DAY;

// File Upload limit
export const MEDIA_FILE_DURATION_LIMIT_FREE = 10; // 10 seconds
export const MEDIA_FILE_DURATION_LIMIT_STARTER = 30; // 30 seconds
export const MEDIA_FILE_DURATION_LIMIT = 60 * 5; // 5 minutes
